<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

/*
 * unifySpaces()
 * Replace any sequence of whitespace with a single space.
 * This helps match texts even if spacing differs.
 */
function unifySpaces($str) {
    $str = preg_replace('/\s+/u', ' ', $str);
    return trim($str);
}

/*
 * toArabicNumeral()
 * Convert a given integer to its Arabic numeral representation.
 */
function toArabicNumeral($num) {
    $arabicDigits = ["٠","١","٢","٣","٤","٥","٦","٧","٨","٩"];
    return implode('', array_map(function($digit) use ($arabicDigits) {
        return $arabicDigits[$digit];
    }, str_split((string)$num)));
}

/*
 * buildChapterDisplay()
 * For a given chapter (from quran.json), build a single string representing
 * the chapter as displayed on the webpage. Each verse is rendered as:
 *
 *    [verse text] + " " + [Arabic numeral for verse id]
 *
 * Verses are separated by a single space.
 *
 * Additionally, this function returns an array of boundaries: each element
 * represents the cumulative end index (in characters) of each verse's display.
 * (That is, the first element is the length of verse 1's display, the second is
 * the length of verse 1 + space + verse 2's display, etc.)
 */
function buildChapterDisplay($chapter) {
    $displayParts = [];
    $boundaries = [];
    $cumulative = 0;
    foreach ($chapter['verses'] as $verse) {
        // Build display text for the verse exactly as rendered.
        // (Note: In the webpage the numeral is appended after a space.)
        $verseDisplay = unifySpaces($verse['text']) . " " . toArabicNumeral($verse['id']);
        $displayParts[] = $verseDisplay;
        $cumulative += mb_strlen($verseDisplay, 'UTF-8') + 1; // +1 for the space between verses
        $boundaries[] = $cumulative;
    }
    // Join all parts with a single space.
    $chapterDisplay = implode(" ", $displayParts);
    return [$chapterDisplay, $boundaries];
}

// ----------------------------------------------------------------------
// 1. Load Quran data (diacritics are preserved exactly as in quran.json)
// ----------------------------------------------------------------------
$quranData = json_decode(file_get_contents('assets/data/quran.json'), true);

// ----------------------------------------------------------------------
// 2. Retrieve input parameters:
//    - "ayat": the user-selected text (which may include numeral markers)
//    - "chapter": (optional) provided by the UI (if "all", search all chapters)
//    - "operator_bahij" and "operator_hafs" determine the bracket pair.
// ----------------------------------------------------------------------
$ayat = htmlspecialchars($_GET['ayat'] ?? ''); 
$chapterParam = $_GET['chapter'] ?? 'all';  // e.g. "all" or a specific chapter id (as given by the UI)
$operator_bahij = $_GET['operator_bahij'] ?? '﴿ - ﴾';
$operator_hafs  = $_GET['operator_hafs'] ?? '';
$operator = !empty($operator_hafs) ? $operator_hafs : $operator_bahij;

// ----------------------------------------------------------------------
// 3. Define the bracket pairs (unchanged)
// ----------------------------------------------------------------------
$brackets = [
    '﴿ - ﴾' => ['﴿', '﴾'],
    '( - )'  => ['(', ')'],
    '[ - ]'  => ['[', ']'],
    '{ - }'  => ['{', '}'],
    '-'      => ['', ''],
    "' - '"  => ["'", "'"],
    '" - "'  => ['"', '"'],
    '" - "'  => ['"', '"'],
    '' - ''  => [''', '''],
    '‹ - ›'  => ['‹', '›'],
    '« - »'  => ['«', '»'],
    '⦅ - ⦆' => ['⦅', '⦆'],
    '〚 - 〛' => ['〚', '〛'],
    '⦃ - ⦄' => ['⦃', '⦄'],
    '﹙ - ﹚' => ['﹙', '﹚'],
    '﹛ - ﹜' => ['﹛', '﹜'],
    '﹝ - ﹞' => ['﹝', '﹞'],
    '⸨ - ⸩' => ['⸨', '⸩'],
    '「 - 」' => ['「', '」'],
    '『 - 』' => ['『', '』'],
    '〈 - 〉' => ['〈', '〉'],
    '《 - 》' => ['《', '》'],
    '【 - 】' => ['【', '】'],
    '〖 - 〗' => ['〖', '〗'],
    '〔 - 〕' => ['〔', '〕'],
    '〘 - 〙' => ['〘', '〙'],
    '⦗ - ⦘' => ['⦗', '⦘'],
    '（ - ）' => ['（', '）'],
    '［ - ］' => ['［', '］'],
    '｛ - ｝' => ['｛', '｝'],
    '｟ - ｠' => ['｟', '｠'],
    '｢ - ｣' => ['｢', '｣'],
    '❜ - ❛' => ['❜', '❛'],
    '❞ - ❝' => ['❞', '❝'],
    '❨ - ❩' => ['❨', '❩'],
    '❪ - ❫' => ['❪', '❫'],
    '❴ - ❵' => ['❴', '❵'],
    '❬ - ❭' => ['❬', '❭'],
    '❮ - ❯' => ['❮', '❯'],
    '❰ - ❱' => ['❰', '❱'],
    '❲ - ❳' => ['❲', '❳'],
    '᚜ - ᚛' => ['᚜', '᚛'],
    '༺ - ༻' => ['༺', '༻'],
    '꧂ - ꧁' => ['꧂', '꧁'],
    // Uthmanic Hafs bracket pairs (swapped order)
    'ﵞ - ﵟ' => ['ﵟ', 'ﵞ'],
    'ﵠ - ﵡ' => ['ﵡ', 'ﵠ'],
    'ﵢ - ﵣ' => ['ﵣ', 'ﵢ'],
    'ﵤ - ﵥ' => ['ﵥ', 'ﵤ'],
    'ﵦ - ﵧ' => ['ﵧ', 'ﵦ'],
    'ﵨ - ﵩ' => ['ﵩ', 'ﵨ'],
    'ﵪ - ﵫ' => ['ﵫ', 'ﵪ'],
    'ﵬ - ﵭ' => ['ﵭ', 'ﵬ'],
    'ﵮ - ﵯ' => ['ﵯ', 'ﵮ'],
    'ﵰ - ﵱ' => ['ﵱ', 'ﵰ'],
    'ﵲ - ﵳ' => ['ﵳ', 'ﵲ'],
    'ﵴ - ﵵ' => ['ﵵ', 'ﵴ'],
    'ﵶ - ﵷ' => ['ﵷ', 'ﵶ'],
    'ﵸ - ﵹ' => ['ﵹ', 'ﵸ'],
    'ﵺ - ﵻ' => ['ﵻ', 'ﵺ'],
    'ﵼ - ﵽ' => ['ﵽ', 'ﵼ']
];

$uthmanicOperators = [
    'ﵞ - ﵟ', 'ﵠ - ﵡ', 'ﵢ - ﵣ', 'ﵤ - ﵥ', 'ﵦ - ﵧ', 'ﵨ - ﵩ', 'ﵪ - ﵫ',
    'ﵬ - ﵭ', 'ﵮ - ﵯ', 'ﵰ - ﵱ', 'ﵲ - ﵳ', 'ﵴ - ﵵ', 'ﵶ - ﵷ', 'ﵸ - ﵹ',
    'ﵺ - ﵻ', 'ﵼ - ﵽ'
];

// ----------------------------------------------------------------------
// 4. Determine candidate chapters to search.
//     If a specific chapter is provided (not "all"), use that chapter;
//     otherwise, search through all chapters.
$candidateChapters = [];
if ($chapterParam !== 'all') {
    foreach ($quranData as $chapter) {
        if ($chapter['id'] == $chapterParam) {
            $candidateChapters[] = $chapter;
            break;
        }
    }
} else {
    $candidateChapters = $quranData;
}

// ----------------------------------------------------------------------
// 5. Normalize the user-selected text.
//    We do NOT remove diacritics here; we only unify spacing.
$selectedText = unifySpaces($ayat);

// ----------------------------------------------------------------------
// 6. Determine the candidate chapter (if not explicitly provided)
//    by searching each candidate chapter's display text for the selected text.
//    (This method finds the chapter that is "above" the selection.)
$foundChapter = null;
$chapterDisplay = "";
$boundaries = [];
foreach ($candidateChapters as $chapter) {
    list($display, $bounds) = buildChapterDisplay($chapter);
    // Use mb_stripos for case–insensitive search (if desired), here we use exact match.
    $pos = mb_stripos($display, $selectedText, 0, 'UTF-8');
    if ($pos !== false) {
        $foundChapter = $chapter;
        $chapterDisplay = $display;
        $boundaries = $bounds;
        break;
    }
}
// If no chapter's combined text contains the selection, fall back to the first candidate.
if (!$foundChapter && !empty($candidateChapters)) {
    $foundChapter = $candidateChapters[0];
    list($chapterDisplay, $boundaries) = buildChapterDisplay($foundChapter);
}

// ----------------------------------------------------------------------
// 7. Determine which verses the selected text spans within the found chapter.
//     If foundChapter is set, we try to determine the start and end verse numbers.
// ----------------------------------------------------------------------
$outputSuffix = "";
if ($foundChapter) {
    // Find the starting index (in characters) of the selected text in the chapter display.
    $startIndex = mb_stripos($chapterDisplay, $selectedText, 0, 'UTF-8');
    if ($startIndex !== false) {
        $endIndex = $startIndex + mb_strlen($selectedText, 'UTF-8');
        // Determine the verse number corresponding to the start index.
        $startVerse = null;
        $endVerse = null;
        foreach ($boundaries as $i => $boundary) {
            if ($startVerse === null && $startIndex < $boundary) {
                // Verses are 1-indexed in quran.json
                $startVerse = $foundChapter['verses'][$i]['id'];
            }
            if ($endIndex <= $boundary) {
                $endVerse = $foundChapter['verses'][$i]['id'];
                break;
            }
        }
        // If endVerse not found (e.g. selection runs to end of chapter), use the last verse.
        if ($endVerse === null) {
            $endVerse = end($foundChapter['verses'])['id'];
        }
        // Build output suffix in the proper format.
        if ($startVerse === $endVerse) {
            $outputSuffix = "({$foundChapter['name']}: {$startVerse})";
        } else {
            $outputSuffix = "({$foundChapter['name']}: {$startVerse}-{$endVerse})";
        }
    }
} else {
    // If no chapter was determined, leave the suffix empty.
    $outputSuffix = "";
}

// ----------------------------------------------------------------------
// 8. Build the final output HTML and plain text.
//     Wrap the original user input in the chosen bracket pair and append the suffix.
// ----------------------------------------------------------------------
$bracketClass = in_array($operator, $uthmanicOperators) ? 'uthmanic-text' : 'bahij-bracket';
list($open, $close) = $brackets[$operator] ?? $brackets['﴿ - ﴾'];

$disableCitation = isset($_GET['disableCitation']) && $_GET['disableCitation'] === "1";

$html = sprintf(
    '<span class="%s">%s</span><span class="uthmanic-text">%s</span><span class="%s">%s</span>%s',
    $bracketClass,
    $open,
    $ayat,  // original user input (with diacritics intact)
    $bracketClass,
    $close,
    (!$disableCitation && $outputSuffix) ? ' ' . $outputSuffix : ''
);


$result = [
    'html' => $html,
    'text' => $open . $ayat . $close . ($outputSuffix ? ' ' . $outputSuffix : '')
];

echo json_encode($result);
?>
