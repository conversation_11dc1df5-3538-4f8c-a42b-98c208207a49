@font-face {
  font-family: "bootstrap-icons";
  src: url("./fonts/bootstrap-icons.woff2?1.11.3") format("woff2"),
    url("./fonts/bootstrap-icons.woff?1.11.3") format("woff");
}

.bi::before,
[class^="bi-"]::before,
[class*=" bi-"]::before {
  display: inline-block;
  font-family: "bootstrap-icons" !important;
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: -0.125em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Alternative approach using SVG background images for offline support */
.bi {
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: -0.125em;
  fill: currentcolor;
}

/* Specific icons used in the application */
.bi-x-circle::before {
  background-image: url("./x-circle.svg");
  background-size: 1em 1em;
  background-repeat: no-repeat;
  background-position: center;
  content: "";
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: -0.125em;
}

.bi-magic::before {
  background-image: url("./star-fill.svg");
  background-size: 1em 1em;
  background-repeat: no-repeat;
  background-position: center;
  content: "";
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: -0.125em;
}

.bi-file-code::before {
  background-image: url("./file-code.svg");
  background-size: 1em 1em;
  background-repeat: no-repeat;
  background-position: center;
  content: "";
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: -0.125em;
}

.bi-clipboard::before {
  background-image: url("./clipboard.svg");
  background-size: 1em 1em;
  background-repeat: no-repeat;
  background-position: center;
  content: "";
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: -0.125em;
}

.bi-info-circle::before {
  background-image: url("./info-circle.svg");
  background-size: 1em 1em;
  background-repeat: no-repeat;
  background-position: center;
  content: "";
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: -0.125em;
}
