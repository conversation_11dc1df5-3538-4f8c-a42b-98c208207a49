/* Bootstrap Icons CSS - Local SVG Version for Offline Support */

.bi::before,
[class^="bi-"]::before,
[class*=" bi-"]::before {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: -0.125em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-size: 1em 1em;
  background-repeat: no-repeat;
  background-position: center;
  content: "";
  width: 1em;
  height: 1em;
}

/* Icons used in the application */
.bi-x-circle::before {
  background-image: url("./x-circle.svg");
}

.bi-magic::before {
  background-image: url("./star-fill.svg");
}

.bi-file-code::before {
  background-image: url("./file-code.svg");
}

.bi-clipboard::before {
  background-image: url("./clipboard.svg");
}

.bi-info-circle::before {
  background-image: url("./info-circle.svg");
}

/* Additional commonly used icons */
.bi-search::before {
  background-image: url("./search.svg");
}

.bi-check::before {
  background-image: url("./check.svg");
}

.bi-check-circle::before {
  background-image: url("./check-circle.svg");
}

.bi-x::before {
  background-image: url("./x.svg");
}

.bi-plus::before {
  background-image: url("./plus.svg");
}

.bi-minus::before {
  background-image: url("./dash.svg");
}

.bi-gear::before {
  background-image: url("./gear.svg");
}

.bi-star::before {
  background-image: url("./star.svg");
}

.bi-star-fill::before {
  background-image: url("./star-fill.svg");
}

.bi-heart::before {
  background-image: url("./heart.svg");
}

.bi-heart-fill::before {
  background-image: url("./heart-fill.svg");
}

.bi-download::before {
  background-image: url("./download.svg");
}

.bi-upload::before {
  background-image: url("./upload.svg");
}

.bi-copy::before {
  background-image: url("./copy.svg");
}

.bi-trash::before {
  background-image: url("./trash.svg");
}

.bi-pencil::before {
  background-image: url("./pencil.svg");
}

.bi-play::before {
  background-image: url("./play.svg");
}

.bi-pause::before {
  background-image: url("./pause.svg");
}

.bi-stop::before {
  background-image: url("./stop.svg");
}
