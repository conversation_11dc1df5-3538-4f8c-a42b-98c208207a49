/* Color Palette:
Primary: #007188
Secondary: #129FBC
Card Background: #EEDFC0
Code Box Background: #EBEBEB
*/
/* Standard selection styling */
::selection {
  background: #007188a4;
  color: #ffffff;
}
::-moz-selection {
  background: #007188a4;
  color: #ffffff;
}
/* Font Faces */
@font-face {
  src: url(../fonts/UthmanicHafsV22.ttf);
  font-family: "Uthmanic Hafs Ver22";
  font-display: swap;
}
@font-face {
  src: url(../fonts/BahijUthmanTaha.ttf);
  font-family: "<PERSON>hij <PERSON>thman <PERSON>";
  font-display: swap;
}
@font-face {
  src: url(../fonts/Rabar15.ttf);
  font-family: "Rabar15";
  font-display: swap;
}
@font-face {
  src: url(../fonts/Rabar13.ttf);
  font-family: "Rabar13";
  font-display: swap;
}
html,
body {
  height: 100vh !important; /* Allow full height adjustment */
  overflow-x: hidden;
  /*overflow-y: hidden;  Allow full page scrolling */
  scroll-behavior: smooth; /* Enables smooth scrolling */
}
/* Body with gradient background */
body {
  margin: 0;
  padding: 0;
  height: 100vh;
  background: #ebebeb;
  line-height: 1.6;
}
.container {
  background: #ebebeb;
}
/* Card and modal backgrounds */

.modal-body,
.modal-header,
.modal-footer {
  background-color: #ebebeb !important;
}
/* Button styles */
.button {
  background: #eedfc0;
  color: #6c757d;
  border: 1px solid darkgray;
  width: 130px;
}
.button:hover {
  background: transparent;
  color: #6c757d;
  transition: all 0.3s;
}
.button:active {
  background: #6c757d;
  color: #ffffff;
}
/* Font classes for text */
.text {
  font-family: "Rabar15";
}
.textb {
  font-family: "Rabar13";
}
strong {
  font-family: "Rabar13";
}
/* Quran preview (if used) */
.quran-preview {
  font-family: "Uthmanic Hafs Ver22";
  font-size: 2rem;
  line-height: 1.8;
  direction: rtl;
  max-height: 50vh !important;
  overflow-y: auto;
}
/* For brackets that should use Bahij Uthman Taha */
.bahij-bracket {
  font-family: "Bahij Uthman Taha" !important;
}
/* For ayat text and Uthmanic bracket pairs */
.uthmanic-text {
  font-family: "Uthmanic Hafs Ver22";
}
/* Styles for select elements */
.sb,
.adb,
select {
  border-color: darkgray;
}
.sb:focus,
.adb:focus,
select:focus {
  border-color: darkgray;
}
.sb,
.sb option {
  font-family: "Bahij Uthman Taha";
}
.adb,
.adb option {
  font-family: "Uthmanic Hafs Ver22";
}
::placeholder {
  font-family: "Bahij Uthman Taha";
}
#codeOutput {
  font-family: monospace;
  direction: ltr;
}
/* Code box styles */
.code-container {
  position: relative;
  background-color: #ffffff;
  border-radius: 6px;
  border: 2px solid #007188;
  padding: 12px;
  overflow-x: auto;
  font-family: monospace;
  direction: ltr;
}
.code-container pre {
  margin: 0;
  background: transparent;
  border: none;
  color: #f8f8f2;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.copy-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #007188;
  color: white;
  border: none;
  padding: 5px 10px;
  font-size: 14px;
  border-radius: 5px;
  cursor: pointer;
  font-family: "Rabar15";
  width: 100px;
}
.copy-btn:hover {
  background: #129fbc;
}
.copy-btn:focus {
  border-color: darkgray;
}
/* Override Bootstrap Primary Button */
.mainButton {
  background-color: #007188;
  border-color: #007188;
}
.mainButton:hover {
  background-color: #129fbc;
  border-color: #129fbc;
}
.mainButton:active {
  background-color: #007188 !important;
  border-color: #007188 !important;
}
.downloadButton {
  background-color: transparent;
  border-color: #007188;
  color: #007188;
}
.downloadButton:hover {
  background-color: #129fbc;
  border-color: #129fbc;
  color: #ffffff;
}
.downloadButton:active {
  background-color: transparent !important;
  border-color: #007188 !important;
  color: #007188;
}
/* Input group border radius styling */
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3),
.input-group:not(.has-validation)
  > .form-floating:not(:last-child)
  > .form-control,
.input-group:not(.has-validation)
  > .form-floating:not(:last-child)
  > .form-select,
.input-group:not(.has-validation)
  > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(
    .form-floating
  ),
.input-group
  > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(
    .valid-feedback
  ):not(.invalid-tooltip):not(.invalid-feedback) {
  border-color: darkgray;
}

/* Sticky filter container (using values from the second style block) */
#filter-container {
  background-color: #ebebeb !important;
  padding: 10px 0;
  margin-bottom: 20px;
  text-align: center;
  position: relative !important; /* Fixes possible scroll blocking */
  width: 100%;
  z-index: 1000;
}
input {
  border-color: darkgray;
}
#matchCount,
#chapterNameDisplay {
  color: maroon;
}
.chapter {
  margin-bottom: 40px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}
.chapter h2 {
  margin-bottom: 10px;
  font-size: 1.5rem;
}
.chapter p {
  font-size: 1.3rem;
  line-height: 1.8;
}
/* Each verse is inline and clickable */
.verse {
  cursor: pointer;
}
/* Highlight styles for search */
.highlight {
  background-color: #eedfc0;
}
.active {
  background-color: #e9c785;
}
.quranBG {
  background-color: #ebebeb !important;
}
.quran-container,
.quran-include {
  height: 100vh; /* Ensures full screen height */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevents the whole page from scrolling */
  background-color: #ebebeb !important;
}
#quranContainer {
  max-height: 80vh; /* Prevents full-page overflow */
  overflow-y: auto; /* Allows scrolling inside */
  scroll-behavior: smooth; /* Enables smooth scrolling */
  padding-right: 10px; /* Prevents cut-off content on the right */
}

#quranContainer,
.chapter,
.verse,
.uthmanic-text,
#searchText {
  font-family: "Uthmanic Hafs Ver22" !important;
  direction: rtl;
}
#quranContainer,
.chapter,
.verse {
  cursor: text !important;
  text-align: justify;
}
#infoBtn {
  position: fixed;
  bottom: 10px;
  left: 10px;
  width: 40px;
  height: 40px;
}

/* PWA Install Prompt - Beautiful Design */
.install-prompt {
  position: fixed;
  bottom: 20px;
  right: 20px;
  max-width: 380px;
  background: linear-gradient(135deg, #007188 0%, #005a6b 100%);
  color: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 113, 136, 0.3), 0 8px 16px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  opacity: 0;
  transform: translateY(100px) scale(0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.install-prompt.show {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.install-prompt-content {
  padding: 20px;
}

.install-prompt-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.install-prompt-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.install-prompt-text {
  flex: 1;
  min-width: 0;
}

.install-prompt-text h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.install-prompt-text p {
  margin: 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.4;
}

.install-prompt-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.install-prompt-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.install-prompt-actions {
  display: flex;
  gap: 12px;
}

.install-btn-primary {
  flex: 1;
  background: white;
  color: #007188;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.install-btn-primary:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.install-btn-primary:active {
  transform: translateY(0);
}

.install-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 12px 20px;
  border-radius: 10px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.install-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Responsive design for mobile */
@media (max-width: 480px) {
  .install-prompt {
    left: 20px;
    right: 20px;
    bottom: 20px;
    max-width: none;
  }

  .install-prompt-text h4 {
    font-size: 16px;
  }

  .install-prompt-text p {
    font-size: 13px;
  }

  .install-prompt-actions {
    flex-direction: column;
  }

  .install-btn-primary,
  .install-btn-secondary {
    width: 100%;
  }
}

.offline-indicator {
  position: fixed;
  top: 10px;
  right: 10px;
  background: #dc3545;
  color: white;
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 12px;
  display: none;
}

.offline-indicator.online {
  background: #28a745;
}

/* Insert text button disabled state */
#insertTextButton:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  pointer-events: auto !important; /* Allow hover to show cursor */
}

#insertTextButton:disabled:hover {
  cursor: not-allowed !important;
  background-color: #007188 !important; /* Keep original color */
  border-color: #007188 !important;
}
