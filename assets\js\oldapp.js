// Global variables for Quran functionality
let chaptersData = [];
let searchMatches = [];
let currentMatchIndex = 0;

// ---------------------------
// Common Utility Functions
// ---------------------------

function copyWithFeedback(btn, content) {
  if (btn._copyTimeoutID) {
    clearTimeout(btn._copyTimeoutID);
    const textSpan = btn.querySelector(".copy-text");
    if (textSpan) {
      textSpan.innerText = btn.dataset.originalText || "کۆپیکردن";
    } else {
      btn.innerText = btn.dataset.originalText || "کۆپیکردن";
    }
    btn._copyTimeoutID = null;
  }

  let textSpan = btn.querySelector(".copy-text");
  let originalText = textSpan ? textSpan.innerText : btn.innerText;
  if (!btn.dataset.originalText) {
    btn.dataset.originalText = originalText;
  }

  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(content)
      .then(() => {
        if (textSpan) {
          textSpan.innerText = "کۆپی کرا";
        } else {
          btn.innerText = "کۆپی کرا";
        }
        btn._copyTimeoutID = setTimeout(() => {
          if (textSpan) {
            textSpan.innerText = btn.dataset.originalText;
          } else {
            btn.innerText = btn.dataset.originalText;
          }
          btn._copyTimeoutID = null;
        }, 2000);
      })
      .catch(console.error);
  } else {
    const textArea = document.createElement("textarea");
    textArea.value = content;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand("copy");
      if (textSpan) {
        textSpan.innerText = "کۆپی کرا";
      } else {
        btn.innerText = "کۆپی کرا";
      }
      btn._copyTimeoutID = setTimeout(() => {
        if (textSpan) {
          textSpan.innerText = btn.dataset.originalText;
        } else {
          btn.innerText = btn.dataset.originalText;
        }
        btn._copyTimeoutID = null;
      }, 2000);
    } catch (err) {
      console.error("Fallback error copying:", err);
    }
    document.body.removeChild(textArea);
  }
}

// ---------------------------
// Quran Display Functionality
// ---------------------------

// Helper: Convert number to Arabic numerals
function toArabicNumeral(num) {
  const arabicDigits = ["٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"];
  return String(num)
    .split("")
    .map((d) => arabicDigits[d])
    .join("");
}

// Update the "Verse" dropdown based on the selected chapter.
function updateVerseSelectOptions() {
  const verseSelect = document.getElementById("verseSelect");
  const chapterSelect = document.getElementById("chapterSelect");

  verseSelect.innerHTML = "";
  const defaultOption = document.createElement("option");
  defaultOption.value = "all";
  defaultOption.textContent = "هەموو ئایەتەکان";
  verseSelect.appendChild(defaultOption);

  const selectedChapter = chapterSelect.value;
  if (selectedChapter !== "all") {
    verseSelect.disabled = false;
    const chapter = chaptersData.find((ch) => ch.id == selectedChapter);
    chapter?.verses?.forEach((verse) => {
      const option = document.createElement("option");
      option.value = verse.id;
      option.textContent = toArabicNumeral(verse.id);
      verseSelect.appendChild(option);
    });
  } else {
    verseSelect.disabled = true;
  }
}

async function fetchChapters() {
  try {
    const response = await fetch("quran.json");
    chaptersData = await response.json();
    populateChapterSelect();
    renderChapters();
  } catch (error) {
    console.error("Error fetching quran.json:", error);
  }
}

function populateChapterSelect() {
  const chapterSelect = document.getElementById("chapterSelect");
  chapterSelect.innerHTML = ""; // Clear existing options

  // Add the "All Chapters" option
  const allOption = document.createElement("option");
  allOption.value = "all";
  allOption.textContent = "هەموو سورەتەکان";
  chapterSelect.appendChild(allOption);

  // Add individual chapters
  chaptersData.forEach((chapter) => {
    const option = document.createElement("option");
    option.value = chapter.id;
    option.textContent = "سورة " + chapter.name;
    chapterSelect.appendChild(option);
  });
}

function renderChapters() {
  const quranContainer = document.getElementById("quranContainer");
  const chapterSelect = document.getElementById("chapterSelect");
  quranContainer.innerHTML = "";

  const selectedChapter = chapterSelect.value;
  chaptersData.forEach((chapter) => {
    if (selectedChapter !== "all" && chapter.id != selectedChapter) return;

    const chapterDiv = document.createElement("div");
    chapterDiv.classList.add("chapter");
    chapterDiv.dataset.chapterId = chapter.id;

    const header = document.createElement("h2");
    header.textContent = "سورة " + chapter.name;
    chapterDiv.appendChild(header);

    const p = document.createElement("p");
    if (chapter.verses?.length) {
      chapter.verses.forEach((verse) => {
        const verseSpan = document.createElement("span");
        verseSpan.classList.add("verse");
        verseSpan.dataset.verseId = verse.id;
        verseSpan.textContent = `${verse.text} ${toArabicNumeral(verse.id)}`;
        p.appendChild(verseSpan);
        p.appendChild(document.createTextNode(" "));
      });
    }
    chapterDiv.appendChild(p);
    quranContainer.appendChild(chapterDiv);
  });

  updateVerseSelectOptions();
}

// ---------------------------
// Event Listeners and UI Setup
// ---------------------------
document.addEventListener("DOMContentLoaded", async () => {
  // Common DOM Elements
  const form = document.getElementById("quranForm");
  const livePreview = document.getElementById("livePreview");
  const codeOutput = document.getElementById("codeOutput");
  const previewPanel = document.getElementById("previewPanel");
  const copyCodeBtn = document.getElementById("copyCodeBtn");
  const clearBtn = document.getElementById("clearBtn");
  const infoBtn = document.getElementById("infoBtn");
  const ayatInput = document.getElementById("ayatInput");

  // Quran-related DOM Elements
  const chapterSelect = document.getElementById("chapterSelect");
  const verseSelect = document.getElementById("verseSelect");
  const searchText = document.getElementById("searchText");
  const searchBtn = document.getElementById("searchBtn");
  const resetBtn = document.getElementById("resetBtn");
  const prevBtn = document.getElementById("prevBtn");
  const nextBtn = document.getElementById("nextBtn");

  // ---------------------------
  // Form and Preview Functionality
  // ---------------------------
  form?.addEventListener("submit", async (e) => {
    e.preventDefault();
    const formData = new FormData(form);
    formData.append(
      "disableCitation",
      document.getElementById("disableCitation").checked ? "0" : "1"
    );
    const params = new URLSearchParams(formData);

    try {
      const response = await fetch(`./generate.php?${params}`);
      const { html } = await response.json();
      livePreview.innerHTML = html;
      codeOutput.value = html;
    } catch (error) {
      console.error("Error:", error);
      alert("نەتوانرا دروستبکرێت");
    }
  });
  document
    .getElementById("disableCitation")
    .addEventListener("change", function () {
      form.dispatchEvent(new Event("submit")); // Submit the form automatically
    });

  previewPanel?.addEventListener("click", function () {
    const text = livePreview.textContent;
    if (text) {
      const badge = this.querySelector(".copy-badge");
      copyWithFeedback(badge, text);
    }
  });

  copyCodeBtn?.addEventListener("click", function () {
    if (codeOutput.value) {
      copyWithFeedback(this, codeOutput.value);
    }
  });

  // Initialize Quran functionality
  await fetchChapters();

  // ---------------------------
  // Text Insertion Functionality
  // ---------------------------
  const navContainer =
    document.querySelector(".quran-container .nextPrev") ||
    document.querySelector(".quran-include .nextPrev");
  if (navContainer) {
    const insertButton = document.createElement("button");
    insertButton.innerHTML = 'دەق بنێرە <i class="bi bi-arrow-bar-left"></i>';
    insertButton.id = "insertTextButton";
    insertButton.classList.add(
      "btn",
      "btn-outline-secondary",
      "mainButton",
      "float-start",
      "col-3",
      "text-light",
      "shadow"
    );
    insertButton.disabled = true; // Start disabled
    insertButton.style.opacity = "0.5"; // Visual indication of disabled state
    navContainer.appendChild(insertButton);

    let selectedText = "";
    document.addEventListener("mouseup", () => {
      const selection = window.getSelection();
      selectedText = selection.toString().trim();

      // Check if selection is from quranContainer
      const quranContainer = document.getElementById("quranContainer");
      let isFromQuranContainer = false;

      if (selectedText && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const commonAncestor = range.commonAncestorContainer;

        // Check if the selection is within quranContainer
        if (commonAncestor.nodeType === Node.TEXT_NODE) {
          isFromQuranContainer = quranContainer.contains(
            commonAncestor.parentNode
          );
        } else {
          isFromQuranContainer = quranContainer.contains(commonAncestor);
        }
      }

      // Enable/disable button based on selection from quranContainer
      if (selectedText && isFromQuranContainer) {
        insertButton.disabled = false;
        insertButton.style.opacity = "1";
      } else {
        insertButton.disabled = true;
        insertButton.style.opacity = "0.5";
        if (!selectedText) {
          selectedText = ""; // Clear selectedText if no selection
        }
      }
    });

    insertButton.addEventListener("click", () => {
      if (insertButton.disabled) return; // Don't proceed if button is disabled

      if (ayatInput) {
        ayatInput.value = selectedText;
        // Reset button state after use
        insertButton.disabled = true;
        insertButton.style.opacity = "0.5";
        // Clear selection
        if (window.getSelection) {
          window.getSelection().removeAllRanges();
        }
        // Trigger the "دروستکردن" (Generate) button.
        // Adjust the selector if you have a unique ID or class for your generate button.
        const generateButton =
          document.querySelector("#generateButton") ||
          document.querySelector(".generateButton");
        if (generateButton) {
          generateButton.click();
        } else {
          // Fallback: trigger the first .mainButton that is not the insert button.
          const mainButtons = document.querySelectorAll(".mainButton");
          for (let btn of mainButtons) {
            if (btn.id !== "insertTextButton") {
              btn.click();
              break;
            }
          }
        }
      }
    });
  }

  // ---------------------------
  // Event Listeners for Navigation and Search
  // ---------------------------
  chapterSelect?.addEventListener("change", () => {
    renderChapters();
    updateVerseSelectOptions();
    clearSearchHighlights();
  });

  verseSelect?.addEventListener("change", jumpToVerse);
  searchBtn?.addEventListener("click", performSearch);
  nextBtn?.addEventListener("click", nextMatch);
  prevBtn?.addEventListener("click", prevMatch);
  searchText?.addEventListener(
    "keyup",
    (e) => e.key === "Enter" && performSearch()
  );
  resetBtn?.addEventListener("click", resetSearchAndFilters);
  // Clear button: reset ayat input, bracket dropdowns, Quran preview, and code output.
  clearBtn?.addEventListener("click", () => {
    // Clear the input field.
    if (ayatInput) ayatInput.value = "";

    // Reset the "کەوانەکان" dropdown (operator_bahij) to its default value.
    const operatorBahij = document.getElementById("operator_bahij");
    if (operatorBahij) {
      operatorBahij.value = "﴿ - ﴾"; // Default value as set in the HTML
    }

    // Reset the "کەوانە تایبەتەکان" dropdown (operator_hafs) to its default value.
    const operatorHafs = document.getElementById("operator_hafs");
    if (operatorHafs) {
      operatorHafs.value = ""; // Default empty value
    }

    // Reset the Quran preview to its default content.
    const livePreview = document.getElementById("livePreview");
    if (livePreview) {
      livePreview.innerHTML = `
      <span class="bahij-bracket">﴿</span>
      <span class="uthmanic-text">بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱللرَّحِيمِ</span>
      <span class="bahij-bracket">﴾</span>
    `;
    }

    // Reset the code output field.
    const codeOutput = document.getElementById("codeOutput");
    if (codeOutput) {
      codeOutput.value = ""; // Clear the generated code
    }
  });

  infoBtn?.addEventListener("click", () =>
    new bootstrap.Modal(document.getElementById("infoModal")).show()
  );
});

// ---------------------------
// Quran Search Functions
// ---------------------------
function jumpToVerse() {
  const verseSelect = document.getElementById("verseSelect");
  const selectedVerse = verseSelect.value;
  if (selectedVerse === "all") return;
  const verseSpan = document.querySelector(
    `.chapter:not([style*="display: none"]) .verse[data-verse-id="${selectedVerse}"]`
  );
  if (verseSpan) {
    verseSpan.scrollIntoView({ behavior: "smooth", block: "center" });
    clearActiveHighlights();
    verseSpan.classList.add("active");
  }
}

function clearActiveHighlights() {
  document.querySelectorAll(".verse").forEach((span) => {
    span.classList.remove("active");
  });
}

function clearSearchHighlights() {
  document.querySelectorAll(".verse").forEach((span) => {
    span.classList.remove("highlight", "active");
  });
  searchMatches = [];
  currentMatchIndex = 0;
  const matchCountElem = document.getElementById("matchCount");
  if (matchCountElem) {
    matchCountElem.textContent = "";
  }
}

function normalizeArabic(str) {
  return (
    str
      // Decompose combined characters into base letters + diacritics.
      .normalize("NFD")
      // Remove diacritical marks and other extra symbols:
      // \u0640: Tatweel
      // \u064B-\u065F: All harakat and related marks
      // \u0670: Additional combining mark for Alef
      // \u06D6-\u06ED: Quranic-specific marks
      .replace(/[\u0640\u064B-\u065F\u0670\u06D6-\u06ED]/g, "")
      // Normalize alif and hamza variants to a standard "ا".
      .replace(/[\u0621\u0622\u0623\u0625\u0671]/g, "ا")
      // Collapse multiple "ا" into one (if needed).
      .replace(/ا{2,}/g, "ا")
      .trim()
  );
}

function precomputeNormalizedText(chapters) {
  chapters.forEach((chapter) => {
    chapter.verses.forEach((verse) => {
      verse.normalizedText = normalizeArabic(verse.text);
    });
  });
}

function performSearch() {
  clearSearchHighlights();
  const searchInput = document.getElementById("searchText").value.trim();
  if (!searchInput) return;

  const normalizedSearch = normalizeArabic(searchInput);

  const allVerses = document.querySelectorAll(".verse");
  searchMatches = [];

  allVerses.forEach((span) => {
    // Assuming you stored the normalized version as a data attribute:
    // e.g., <span class="verse" data-norm="...">original text</span>
    const normText = span.dataset.norm || normalizeArabic(span.textContent);
    if (normText.includes(normalizedSearch)) {
      span.classList.add("highlight");
      searchMatches.push(span);
    }
  });

  if (searchMatches.length) {
    currentMatchIndex = 0;
    activateCurrentMatch();
  } else {
    const matchCountElem = document.getElementById("matchCount");
    if (matchCountElem) {
      matchCountElem.textContent = "هیچ نەدۆزرایەوە";
    }
  }
}

function activateCurrentMatch() {
  clearActiveHighlights();
  if (!searchMatches.length) return;

  const currentSpan = searchMatches[currentMatchIndex];
  currentSpan.classList.add("active");
  currentSpan.scrollIntoView({ behavior: "smooth", block: "center" });

  // Update match count display
  const matchCountElem = document.getElementById("matchCount");
  if (matchCountElem) {
    matchCountElem.textContent = `${currentMatchIndex + 1} لە ${
      searchMatches.length
    }`;
  }

  // NEW: Update chapter name display
  const chapterNameElem = document.getElementById("chapterNameDisplay");
  if (chapterNameElem) {
    // Find the closest parent element with class "chapter"
    const chapterDiv = currentSpan.closest(".chapter");
    if (chapterDiv) {
      // Extract chapter name from the h2 header inside the chapter div
      const header = chapterDiv.querySelector("h2");
      chapterNameElem.textContent = header ? header.textContent : "";
    } else {
      chapterNameElem.textContent = "";
    }
  }
}

function nextMatch() {
  if (searchMatches.length) {
    currentMatchIndex = (currentMatchIndex + 1) % searchMatches.length;
    activateCurrentMatch();
  }
}

function prevMatch() {
  if (searchMatches.length) {
    currentMatchIndex =
      (currentMatchIndex - 1 + searchMatches.length) % searchMatches.length;
    activateCurrentMatch();
  }
}

function resetSearchAndFilters() {
  const searchText = document.getElementById("searchText");
  if (searchText) searchText.value = "";

  const chapterSelect = document.getElementById("chapterSelect");
  if (chapterSelect) chapterSelect.value = "all";

  const chapterNameDisplay = document.getElementById("chapterNameDisplay");
  if (chapterNameDisplay) chapterNameDisplay.innerText = "";

  clearSearchHighlights();
  renderChapters();

  const insertButton = document.getElementById("insertTextButton");
  if (insertButton) {
    insertButton.disabled = true;
    insertButton.style.opacity = "0.5";
  }

  if (window.getSelection) {
    window.getSelection().removeAllRanges();
  }

  // **Scroll only inside `#quranContainer` smoothly**
  const quranContainer = document.getElementById("quranContainer");
  if (quranContainer) {
    quranContainer.scrollTo({ top: 0, behavior: "smooth" });
  }
}

// ---------------------------
// CSS Copy Functionality
// ---------------------------
function copyCssCode(btn) {
  const codeElement = document.getElementById("cssCode");
  copyWithFeedback(btn, codeElement?.innerText?.trim() || "");
}
